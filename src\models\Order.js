const mongoose = require('mongoose');
const { ORDER_STATUS, PAYMENT_STATUS, PAYMENT_METHODS } = require('../utils/constants');

const orderItemSchema = new mongoose.Schema({
  menuItemId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  variants: [String],
  addons: [{
    name: String,
    price: Number,
    quantity: Number
  }],
  specialInstructions: String,
  itemTotal: {
    type: Number,
    required: true,
    min: 0
  }
});

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    required: true,
    unique: true
  },
  franchiseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Franchise',
    required: true
  },
  customerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  restaurantId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Restaurant',
    required: true
  },
  deliveryPartnerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  items: [orderItemSchema],
  pricing: {
    itemsSubtotal: {
      type: Number,
      required: true,
      min: 0
    },
    taxes: {
      cgst: { type: Number, default: 0 },
      sgst: { type: Number, default: 0 },
      igst: { type: Number, default: 0 },
      total: { type: Number, default: 0 }
    },
    deliveryFee: {
      type: Number,
      default: 0,
      min: 0
    },
    platformFee: {
      type: Number,
      default: 0,
      min: 0
    },
    discounts: {
      couponCode: String,
      discountAmount: {
        type: Number,
        default: 0,
        min: 0
      },
      description: String
    },
    total: {
      type: Number,
      required: true,
      min: 0
    }
  },
  addresses: {
    pickup: {
      restaurantName: String,
      address: String,
      coordinates: [Number],
      contactNumber: String
    },
    delivery: {
      customerName: String,
      address: String,
      coordinates: [Number],
      contactNumber: String,
      landmark: String,
      deliveryInstructions: String
    }
  },
  timeline: [{
    status: {
      type: String,
      enum: Object.values(ORDER_STATUS),
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    note: String,
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    estimatedTime: Number
  }],
  currentStatus: {
    type: String,
    enum: Object.values(ORDER_STATUS),
    default: ORDER_STATUS.PLACED
  },
  delivery: {
    estimatedPickupTime: Date,
    estimatedDeliveryTime: Date,
    actualPickupTime: Date,
    actualDeliveryTime: Date,
    deliveryDistance: Number,
    deliveryRoute: [[Number]],
    deliveryPartnerLocation: [Number],
    deliveryOTP: {
      type: String,
      length: 4
    }
  },
  payment: {
    method: {
      type: String,
      enum: Object.values(PAYMENT_METHODS),
      required: true
    },
    gateway: String,
    transactionId: String,
    status: {
      type: String,
      enum: Object.values(PAYMENT_STATUS),
      default: PAYMENT_STATUS.PENDING
    },
    paidAmount: {
      type: Number,
      default: 0
    },
    refundAmount: {
      type: Number,
      default: 0
    },
    paymentTimestamp: Date,
    gatewayResponse: mongoose.Schema.Types.Mixed
  },
  feedback: {
    customerRating: {
      type: Number,
      min: 1,
      max: 5
    },
    customerReview: String,
    restaurantRating: {
      type: Number,
      min: 1,
      max: 5
    },
    deliveryRating: {
      type: Number,
      min: 1,
      max: 5
    },
    reviewTimestamp: Date
  },
  cancellation: {
    reason: String,
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    cancelledAt: Date,
    refundAmount: Number,
    refundStatus: {
      type: String,
      enum: ['pending', 'processed', 'failed'],
      default: 'pending'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

orderSchema.index({ orderNumber: 1 }, { unique: true });
orderSchema.index({ customerId: 1, createdAt: -1 });
orderSchema.index({ restaurantId: 1, currentStatus: 1 });
orderSchema.index({ deliveryPartnerId: 1, currentStatus: 1 });
orderSchema.index({ franchiseId: 1, createdAt: -1 });
orderSchema.index({ currentStatus: 1 });
orderSchema.index({ 'payment.status': 1 });
orderSchema.index({ createdAt: -1 });

orderSchema.pre('save', function(next) {
  if (this.isNew && !this.orderNumber) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    this.orderNumber = `ORD-${year}${month}${day}-${random}`;
  }
  next();
});

orderSchema.methods.addTimelineEntry = function(status, note, updatedBy, estimatedTime) {
  this.timeline.push({
    status,
    timestamp: new Date(),
    note,
    updatedBy,
    estimatedTime
  });
  this.currentStatus = status;
};

orderSchema.methods.calculateTotal = function() {
  const itemsTotal = this.items.reduce((sum, item) => sum + item.itemTotal, 0);
  const taxTotal = this.pricing.taxes.total || 0;
  const deliveryFee = this.pricing.deliveryFee || 0;
  const platformFee = this.pricing.platformFee || 0;
  const discountAmount = this.pricing.discounts.discountAmount || 0;
  
  this.pricing.itemsSubtotal = itemsTotal;
  this.pricing.total = itemsTotal + taxTotal + deliveryFee + platformFee - discountAmount;
  
  return this.pricing.total;
};

orderSchema.methods.canBeCancelled = function() {
  const cancellableStatuses = [ORDER_STATUS.PLACED, ORDER_STATUS.ACCEPTED];
  return cancellableStatuses.includes(this.currentStatus);
};

orderSchema.methods.generateDeliveryOTP = function() {
  this.delivery.deliveryOTP = Math.floor(1000 + Math.random() * 9000).toString();
  return this.delivery.deliveryOTP;
};

orderSchema.virtual('estimatedDeliveryDuration').get(function() {
  if (this.delivery.estimatedDeliveryTime && this.createdAt) {
    return Math.ceil((this.delivery.estimatedDeliveryTime - this.createdAt) / (1000 * 60));
  }
  return null;
});

orderSchema.virtual('actualDeliveryDuration').get(function() {
  if (this.delivery.actualDeliveryTime && this.createdAt) {
    return Math.ceil((this.delivery.actualDeliveryTime - this.createdAt) / (1000 * 60));
  }
  return null;
});

module.exports = mongoose.model('Order', orderSchema);
