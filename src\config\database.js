const mongoose = require('mongoose');
const pgManager = require('./postgresql');
const logger = require('../utils/logger');

class DatabaseManager {
  constructor() {
    this.mongoConnected = false;
    this.pgConnected = false;
  }

  async connect() {
    try {
      // Connect to MongoDB
      await this.connectMongoDB();

      // Connect to PostgreSQL
      await this.connectPostgreSQL();

      logger.info('🎉 All databases connected successfully');
    } catch (error) {
      logger.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  async connectMongoDB() {
    try {
      if (this.mongoConnected) {
        logger.info('MongoDB already connected');
        return;
      }

      const mongoUri = process.env.NODE_ENV === 'test' 
        ? process.env.MONGODB_TEST_URI 
        : process.env.MONGODB_URI;

      await mongoose.connect(mongoUri, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000
      });

      this.mongoConnected = true;
      logger.info('✅ MongoDB connected successfully');

      mongoose.connection.on('error', (err) => {
        logger.error('❌ MongoDB connection error:', err);
        this.mongoConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('⚠️ MongoDB disconnected');
        this.mongoConnected = false;
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('✅ MongoDB reconnected');
        this.mongoConnected = true;
      });

    } catch (error) {
      logger.error('❌ MongoDB connection failed:', {
        message: error.message,
        code: error.code,
        name: error.name,
        stack: error.stack
      });
      this.mongoConnected = false;
      throw error;
    }
  }

  async connectPostgreSQL() {
    try {
      await pgManager.connect();
      this.pgConnected = true;
    } catch (error) {
      logger.error('❌ PostgreSQL connection failed:', error);
      this.pgConnected = false;
      throw error;
    }
  }

  async disconnect() {
    try {
      // Disconnect MongoDB
      if (this.mongoConnected) {
        await mongoose.disconnect();
        this.mongoConnected = false;
        logger.info('🔌 MongoDB disconnected');
      }

      // Disconnect PostgreSQL
      if (this.pgConnected) {
        await pgManager.disconnect();
        this.pgConnected = false;
      }

      logger.info('🔌 All databases disconnected');
    } catch (error) {
      logger.error('Error disconnecting from databases:', error);
      throw error;
    }
  }

  getConnectionStatus() {
    return {
      mongodb: {
        isConnected: this.mongoConnected,
        readyState: mongoose.connection.readyState,
        host: mongoose.connection.host,
        name: mongoose.connection.name
      },
      postgresql: pgManager.getConnectionStatus()
    };
  }

  // Expose PostgreSQL manager for direct access
  get pg() {
    return pgManager;
  }
}

const dbManager = new DatabaseManager();

module.exports = dbManager;
