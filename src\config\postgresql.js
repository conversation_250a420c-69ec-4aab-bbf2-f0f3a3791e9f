const { Pool } = require('pg');
const logger = require('../utils/logger');

class PostgreSQLManager {
  constructor() {
    this.pool = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      if (this.isConnected) {
        logger.info('PostgreSQL already connected');
        return this.pool;
      }

      this.pool = new Pool({
        host: process.env.POSTGRESQL_HOST || 'localhost',
        port: process.env.POSTGRESQL_PORT || 5432,
        user: process.env.POSTGRESQL_USER || 'myuser',
        password: process.env.POSTGRESQL_PASSWORD || 'mypassword',
        database: process.env.POSTGRESQL_DB || 'mydatabase',
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });

      // Test the connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();

      this.isConnected = true;
      logger.info('✅ PostgreSQL connected successfully');

      // Set up error handlers
      this.pool.on('error', (err) => {
        logger.error('❌ PostgreSQL pool error:', err);
        this.isConnected = false;
      });

      return this.pool;
    } catch (error) {
      logger.error('❌ PostgreSQL connection failed:', error);
      this.isConnected = false;
      throw error;
    }
  }

  async disconnect() {
    try {
      if (!this.pool) {
        return;
      }

      await this.pool.end();
      this.isConnected = false;
      logger.info('🔌 PostgreSQL disconnected');
    } catch (error) {
      logger.error('Error disconnecting from PostgreSQL:', error);
      throw error;
    }
  }

  async query(text, params) {
    try {
      if (!this.pool) {
        throw new Error('PostgreSQL not connected');
      }

      const start = Date.now();
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;

      logger.debug('PostgreSQL query executed', {
        query: text,
        duration: `${duration}ms`,
        rows: result.rowCount
      });

      return result;
    } catch (error) {
      logger.error('PostgreSQL query error:', {
        query: text,
        error: error.message
      });
      throw error;
    }
  }

  async getClient() {
    if (!this.pool) {
      throw new Error('PostgreSQL not connected');
    }
    return await this.pool.connect();
  }

  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      totalCount: this.pool?.totalCount || 0,
      idleCount: this.pool?.idleCount || 0,
      waitingCount: this.pool?.waitingCount || 0
    };
  }
}

const pgManager = new PostgreSQLManager();

module.exports = pgManager;
