{"name": "food-delivery-api", "version": "1.0.0", "description": "Centralized API for Franchise Food Delivery Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setup:db": "node scripts/setup-databases.js", "setup:mongodb": "mongosh food_delivery_platform scripts/setup-mongodb.js", "setup:postgresql": "psql -h localhost -U myuser -d mydatabase -f scripts/setup-postgresql.sql"}, "keywords": ["food-delivery", "franchise", "api", "nodejs"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.19.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "morgan": "^1.10.0", "pg": "^8.16.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}}