const mongoose = require('mongoose');
const { FRANCHISE_STATUS } = require('../utils/constants');

const franchiseSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    minlength: 3,
    maxlength: 100
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  owner: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Invalid email format']
    },
    phone: {
      type: String,
      required: true,
      match: [/^\+[1-9]\d{1,14}$/, 'Invalid phone number format']
    },
    documents: {
      pan: String,
      gst: String,
      bankAccount: {
        number: String,
        ifsc: String,
        name: String
      }
    }
  },
  location: {
    address: {
      type: String,
      required: true
    },
    coordinates: {
      type: [Number],
      required: true,
      validate: {
        validator: function(coords) {
          return coords.length === 2 && 
                 coords[0] >= -180 && coords[0] <= 180 && 
                 coords[1] >= -90 && coords[1] <= 90;
        },
        message: 'Invalid coordinates format'
      }
    },
    serviceAreas: [{
      type: {
        type: String,
        enum: ['Polygon', 'Circle'],
        default: 'Circle'
      },
      coordinates: [[Number]],
      center: [Number],
      radius: Number
    }],
    city: {
      type: String,
      required: true
    },
    state: {
      type: String,
      required: true
    },
    country: {
      type: String,
      required: true,
      default: 'India'
    },
    pincode: String
  },
  businessSettings: {
    operatingHours: {
      monday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      tuesday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      wednesday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      thursday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      friday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      saturday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      sunday: { open: String, close: String, isOpen: { type: Boolean, default: true } }
    },
    deliverySettings: {
      minOrderAmount: {
        type: Number,
        default: 100,
        min: 0
      },
      maxDeliveryDistance: {
        type: Number,
        default: 10,
        min: 1
      },
      avgDeliveryTime: {
        type: Number,
        default: 30,
        min: 10
      },
      deliveryFee: {
        base: {
          type: Number,
          default: 25,
          min: 0
        },
        perKm: {
          type: Number,
          default: 5,
          min: 0
        },
        freeDeliveryAbove: {
          type: Number,
          default: 300,
          min: 0
        }
      }
    }
  },
  commissionSettings: {
    restaurantCommission: {
      type: Number,
      required: true,
      min: 0,
      max: 1,
      default: 0.18
    },
    deliveryCommission: {
      type: Number,
      required: true,
      min: 0,
      max: 1,
      default: 0.15
    },
    platformFee: {
      type: Number,
      required: true,
      min: 0,
      max: 1,
      default: 0.02
    },
    paymentGatewayFee: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.025
    }
  },
  status: {
    type: String,
    enum: Object.values(FRANCHISE_STATUS),
    default: FRANCHISE_STATUS.ACTIVE
  },
  onboardingDate: {
    type: Date,
    default: Date.now
  },
  lastActive: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

franchiseSchema.index({ 'location.coordinates': '2dsphere' });
franchiseSchema.index({ slug: 1 }, { unique: true });
franchiseSchema.index({ 'owner.email': 1 }, { unique: true });
franchiseSchema.index({ status: 1 });
franchiseSchema.index({ 'location.city': 1, status: 1 });

franchiseSchema.virtual('totalRestaurants', {
  ref: 'Restaurant',
  localField: '_id',
  foreignField: 'franchiseId',
  count: true
});

franchiseSchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.slug = this.name.toLowerCase()
                      .replace(/[^a-z0-9\s-]/g, '')
                      .replace(/\s+/g, '-')
                      .replace(/-+/g, '-')
                      .trim('-');
  }
  next();
});

franchiseSchema.methods.servesLocation = function(coordinates) {
  const [longitude, latitude] = coordinates;
  
  for (const area of this.location.serviceAreas) {
    if (area.type === 'Circle') {
      const distance = this.calculateDistance(area.center, coordinates);
      if (distance <= area.radius) return true;
    }
  }
  
  return false;
};

franchiseSchema.methods.calculateDistance = function(point1, point2) {
  const [lon1, lat1] = point1;
  const [lon2, lat2] = point2;
  
  const R = 6371e3;
  const φ1 = lat1 * Math.PI/180;
  const φ2 = lat2 * Math.PI/180;
  const Δφ = (lat2-lat1) * Math.PI/180;
  const Δλ = (lon2-lon1) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c;
};

module.exports = mongoose.model('Franchise', franchiseSchema);
