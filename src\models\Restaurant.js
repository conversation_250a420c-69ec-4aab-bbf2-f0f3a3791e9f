const mongoose = require('mongoose');
const { RESTAURANT_STATUS } = require('../utils/constants');

const menuItemSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  originalPrice: {
    type: Number,
    min: 0
  },
  images: [String],
  isVegetarian: {
    type: Boolean,
    default: false
  },
  isVegan: {
    type: Boolean,
    default: false
  },
  spiceLevel: {
    type: String,
    enum: ['mild', 'medium', 'hot'],
    default: 'medium'
  },
  ingredients: [String],
  allergens: [String],
  nutritionalInfo: {
    calories: Number,
    protein: Number,
    carbs: Number,
    fat: Number
  },
  preparationTime: {
    type: Number,
    default: 15
  },
  isAvailable: {
    type: Boolean,
    default: true
  },
  variants: [{
    name: String,
    price: Number,
    isDefault: {
      type: Boolean,
      default: false
    }
  }],
  addons: [{
    name: String,
    price: Number,
    isAvailable: {
      type: Boolean,
      default: true
    }
  }]
});

const restaurantSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  franchiseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Franchise',
    required: true
  },
  ownerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  businessInfo: {
    type: {
      type: String,
      enum: ['restaurant', 'cloud_kitchen', 'cafe'],
      default: 'restaurant'
    },
    cuisine: [{
      type: String,
      required: true
    }],
    description: {
      type: String,
      required: true
    },
    images: [String],
    license: {
      fssai: {
        type: String,
        match: [/^\d{14}$/, 'FSSAI license must be 14 digits']
      },
      gst: {
        type: String,
        match: [/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Invalid GST format']
      }
    }
  },
  location: {
    address: {
      type: String,
      required: true
    },
    coordinates: {
      type: [Number],
      required: true,
      validate: {
        validator: function(coords) {
          return coords.length === 2 && 
                 coords[0] >= -180 && coords[0] <= 180 && 
                 coords[1] >= -90 && coords[1] <= 90;
        },
        message: 'Invalid coordinates format'
      }
    },
    landmark: String,
    deliveryZones: [{
      type: {
        type: String,
        enum: ['Circle', 'Polygon'],
        default: 'Circle'
      },
      center: [Number],
      radius: Number,
      coordinates: [[Number]]
    }]
  },
  operationalInfo: {
    businessHours: {
      monday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      tuesday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      wednesday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      thursday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      friday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      saturday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      sunday: { open: String, close: String, isOpen: { type: Boolean, default: true } }
    },
    avgPreparationTime: {
      type: Number,
      default: 25
    },
    isCurrentlyOpen: {
      type: Boolean,
      default: true
    },
    acceptingOrders: {
      type: Boolean,
      default: true
    },
    minOrderAmount: {
      type: Number,
      default: 100,
      min: 0
    }
  },
  menu: [menuItemSchema],
  ratings: {
    average: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    },
    count: {
      type: Number,
      default: 0
    },
    breakdown: {
      5: { type: Number, default: 0 },
      4: { type: Number, default: 0 },
      3: { type: Number, default: 0 },
      2: { type: Number, default: 0 },
      1: { type: Number, default: 0 }
    }
  },
  financials: {
    commissionRate: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.18
    },
    settlementCycle: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
      default: 'weekly'
    },
    bankDetails: {
      accountNumber: String,
      ifsc: String,
      accountName: String
    }
  },
  performance: {
    totalOrders: {
      type: Number,
      default: 0
    },
    avgRating: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    },
    avgDeliveryTime: {
      type: Number,
      default: 35
    },
    acceptanceRate: {
      type: Number,
      min: 0,
      max: 1,
      default: 1
    },
    cancellationRate: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    }
  },
  status: {
    type: String,
    enum: Object.values(RESTAURANT_STATUS),
    default: RESTAURANT_STATUS.PENDING
  },
  verificationStatus: {
    type: String,
    enum: ['pending', 'verified', 'rejected'],
    default: 'pending'
  },
  onboardingDate: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

restaurantSchema.index({ 'location.coordinates': '2dsphere' });
restaurantSchema.index({ franchiseId: 1, status: 1 });
restaurantSchema.index({ slug: 1 }, { unique: true });
restaurantSchema.index({ ownerId: 1 });
restaurantSchema.index({ 'businessInfo.cuisine': 1 });
restaurantSchema.index({ 'ratings.average': -1 });
restaurantSchema.index({ 'operationalInfo.isCurrentlyOpen': 1 });
restaurantSchema.index({ name: 'text', 'businessInfo.description': 'text', 'businessInfo.cuisine': 'text' });

restaurantSchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.slug = this.name.toLowerCase()
                      .replace(/[^a-z0-9\s-]/g, '')
                      .replace(/\s+/g, '-')
                      .replace(/-+/g, '-')
                      .trim('-');
  }
  next();
});

restaurantSchema.methods.isOpenNow = function() {
  const now = new Date();
  const day = now.toLocaleLowerCase().substring(0, 3);
  const currentTime = now.toTimeString().substring(0, 5);
  
  const todayHours = this.operationalInfo.businessHours[day];
  if (!todayHours || !todayHours.isOpen) return false;
  
  return currentTime >= todayHours.open && currentTime <= todayHours.close;
};

restaurantSchema.methods.canDeliverTo = function(coordinates) {
  for (const zone of this.location.deliveryZones) {
    if (zone.type === 'Circle') {
      const distance = this.calculateDistance(zone.center, coordinates);
      if (distance <= zone.radius) return true;
    }
  }
  return false;
};

restaurantSchema.methods.calculateDistance = function(point1, point2) {
  const [lon1, lat1] = point1;
  const [lon2, lat2] = point2;
  
  const R = 6371e3;
  const φ1 = lat1 * Math.PI/180;
  const φ2 = lat2 * Math.PI/180;
  const Δφ = (lat2-lat1) * Math.PI/180;
  const Δλ = (lon2-lon1) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c;
};

module.exports = mongoose.model('Restaurant', restaurantSchema);
