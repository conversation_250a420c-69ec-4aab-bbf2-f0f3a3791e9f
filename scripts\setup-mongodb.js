// Food Delivery Platform - MongoDB Setup Script
// Run this script to create collections with validation and indexes

// Connect to the database
use food_delivery_platform;

// Create users collection with validation
db.createCollection("users", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["email", "phone", "role", "profile"],
      properties: {
        email: { 
          bsonType: "string", 
          pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$" 
        },
        phone: { 
          bsonType: "string", 
          pattern: "^\\+[1-9]\\d{1,14}$" 
        },
        role: { 
          enum: ["customer", "restaurant_owner", "delivery_partner", "franchise_admin", "super_admin"] 
        },
        status: { 
          enum: ["active", "inactive", "suspended", "pending_verification"] 
        },
        "profile.name": {
          bsonType: "string",
          minLength: 2,
          maxLength: 100
        }
      }
    }
  }
});

// Create franchises collection with validation
db.createCollection("franchises", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["name", "owner", "location", "commissionSettings"],
      properties: {
        name: { 
          bsonType: "string", 
          minLength: 3, 
          maxLength: 100 
        },
        "owner.email": { 
          bsonType: "string", 
          pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$" 
        },
        "owner.phone": { 
          bsonType: "string", 
          pattern: "^\\+[1-9]\\d{1,14}$" 
        },
        "location.coordinates": { 
          bsonType: "array",
          minItems: 2,
          maxItems: 2,
          items: { bsonType: "double" }
        },
        status: { 
          enum: ["active", "inactive", "suspended"] 
        }
      }
    }
  }
});

// Create restaurants collection with validation
db.createCollection("restaurants", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["name", "franchiseId", "ownerId", "businessInfo", "location"],
      properties: {
        name: { 
          bsonType: "string", 
          minLength: 2, 
          maxLength: 100 
        },
        franchiseId: { bsonType: "objectId" },
        ownerId: { bsonType: "objectId" },
        "location.coordinates": { 
          bsonType: "array",
          minItems: 2,
          maxItems: 2,
          items: { bsonType: "double" }
        },
        status: { 
          enum: ["pending", "active", "inactive", "suspended"] 
        },
        verificationStatus: { 
          enum: ["pending", "verified", "rejected"] 
        }
      }
    }
  }
});

// Create orders collection with validation
db.createCollection("orders", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["orderNumber", "franchiseId", "customerId", "restaurantId", "items", "pricing"],
      properties: {
        orderNumber: { 
          bsonType: "string",
          pattern: "^ORD-\\d{8}-\\d{4}$"
        },
        franchiseId: { bsonType: "objectId" },
        customerId: { bsonType: "objectId" },
        restaurantId: { bsonType: "objectId" },
        currentStatus: { 
          enum: ["placed", "accepted", "preparing", "ready", "picked_up", "out_for_delivery", "delivered", "cancelled"] 
        },
        "payment.method": { 
          enum: ["online", "cod"] 
        },
        "payment.status": { 
          enum: ["pending", "completed", "failed", "refunded"] 
        }
      }
    }
  }
});

// Create reviews collection
db.createCollection("reviews", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["orderId", "customerId", "restaurantId", "ratings"],
      properties: {
        orderId: { bsonType: "objectId" },
        customerId: { bsonType: "objectId" },
        restaurantId: { bsonType: "objectId" },
        "ratings.overall": { 
          bsonType: "number",
          minimum: 1,
          maximum: 5
        },
        status: { 
          enum: ["published", "hidden", "flagged"] 
        }
      }
    }
  }
});

// Create coupons collection
db.createCollection("coupons", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["code", "title", "discountType", "discountValue"],
      properties: {
        code: { 
          bsonType: "string",
          minLength: 3,
          maxLength: 20
        },
        discountType: { 
          enum: ["percentage", "fixed", "free_delivery"] 
        },
        discountValue: { 
          bsonType: "number",
          minimum: 0
        }
      }
    }
  }
});

print("✅ Collections created with validation rules");

// Create indexes for users
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "phone": 1, "role": 1 }, { unique: true });
db.users.createIndex({ "franchiseId": 1 });
db.users.createIndex({ "role": 1 });
db.users.createIndex({ "status": 1 });
db.users.createIndex({ "profile.addresses.coordinates": "2dsphere" });
db.users.createIndex({ "profile.deliveryPartnerInfo.isOnline": 1 });

// Create indexes for franchises
db.franchises.createIndex({ "location.coordinates": "2dsphere" });
db.franchises.createIndex({ "slug": 1 }, { unique: true });
db.franchises.createIndex({ "owner.email": 1 }, { unique: true });
db.franchises.createIndex({ "status": 1 });
db.franchises.createIndex({ "location.city": 1, "status": 1 });

// Create indexes for restaurants
db.restaurants.createIndex({ "location.coordinates": "2dsphere" });
db.restaurants.createIndex({ "franchiseId": 1, "status": 1 });
db.restaurants.createIndex({ "slug": 1 }, { unique: true });
db.restaurants.createIndex({ "ownerId": 1 });
db.restaurants.createIndex({ "businessInfo.cuisine": 1 });
db.restaurants.createIndex({ "ratings.average": -1 });
db.restaurants.createIndex({ "operationalInfo.isCurrentlyOpen": 1 });

// Create text index for restaurant search
db.restaurants.createIndex({
  "name": "text",
  "businessInfo.description": "text",
  "businessInfo.cuisine": "text"
});

// Create indexes for orders
db.orders.createIndex({ "orderNumber": 1 }, { unique: true });
db.orders.createIndex({ "customerId": 1, "createdAt": -1 });
db.orders.createIndex({ "restaurantId": 1, "currentStatus": 1 });
db.orders.createIndex({ "deliveryPartnerId": 1, "currentStatus": 1 });
db.orders.createIndex({ "franchiseId": 1, "createdAt": -1 });
db.orders.createIndex({ "currentStatus": 1 });
db.orders.createIndex({ "payment.status": 1 });
db.orders.createIndex({ "createdAt": -1 });

// Create indexes for reviews
db.reviews.createIndex({ "restaurantId": 1, "status": 1, "createdAt": -1 });
db.reviews.createIndex({ "customerId": 1, "createdAt": -1 });
db.reviews.createIndex({ "orderId": 1 }, { unique: true });
db.reviews.createIndex({ "ratings.overall": -1 });

// Create indexes for coupons
db.coupons.createIndex({ "code": 1 }, { unique: true });
db.coupons.createIndex({ "validity.isActive": 1, "validity.endDate": 1 });
db.coupons.createIndex({ "conditions.applicableFranchises": 1 });

print("✅ Indexes created successfully");

// Create a sample super admin user (optional)
// Uncomment the following lines to create a default super admin
/*
db.users.insertOne({
  email: "<EMAIL>",
  phone: "+919999999999",
  role: "super_admin",
  profile: {
    name: "Super Admin"
  },
  authentication: {
    passwordHash: "$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS", // password: admin123
    emailVerified: true,
    phoneVerified: true
  },
  status: "active",
  settings: {
    notifications: {
      push: true,
      sms: true,
      email: true
    },
    language: "en",
    currency: "INR"
  }
});
*/

print("🎉 MongoDB setup completed successfully!");
print("📊 Database: food_delivery_platform");
print("📋 Collections created: users, franchises, restaurants, orders, reviews, coupons");
print("🔍 Indexes created for optimal performance");
print("✅ Ready to use with your Food Delivery API!");
