# Phase 1: Database & API Development Plan
*Franchise Food Delivery Platform - Foundation Development*

## Database Solution Recommendation

### **Recommended Approach: MongoDB + PostgreSQL Hybrid**

After analyzing your franchise food delivery requirements, I recommend a **hybrid database approach**:

#### **Primary Database: MongoDB** 
**Use for:**
- **Orders & Real-time Data** (90% of operations)
- User profiles and preferences
- Restaurant menus and dynamic content
- Real-time tracking data
- Analytics and logging data

**Why MongoDB is Perfect Here:**
✅ **Horizontal Scaling**: Essential for multi-franchise growth  
✅ **Flexible Schema**: Menu items, user preferences vary greatly  
✅ **Real-time Performance**: Sub-millisecond reads for order tracking  
✅ **Geospatial Queries**: Built-in location-based search  
✅ **Multi-tenant Architecture**: Easy franchise data isolation  
✅ **JSON-native**: Perfect for mobile app APIs  

#### **Secondary Database: PostgreSQL**
**Use for:**
- **Financial transactions & settlements**
- Commission calculations
- Audit trails and compliance
- Complex reporting queries
- Master data (franchise configs, tax rates)

**Why PostgreSQL for Financial Data:**
✅ **ACID Compliance**: Critical for money transactions  
✅ **Complex Queries**: Financial reporting and analytics  
✅ **Data Integrity**: Foreign key constraints for critical relationships  
✅ **Mature Ecosystem**: Proven for financial applications  

### **Architecture Decision:**

```
┌─────────────────────┐    ┌─────────────────────┐
│     MongoDB         │    │    PostgreSQL       │
│   (Primary DB)      │    │   (Financial DB)    │
├─────────────────────┤    ├─────────────────────┤
│ • Orders            │    │ • Transactions      │
│ • Users             │    │ • Settlements       │
│ • Restaurants       │    │ • Commission Rules  │
│ • Real-time Data    │    │ • Audit Logs        │
│ • Menus             │    │ • Tax Calculations  │
│ • Reviews           │    │ • Financial Reports │
└─────────────────────┘    └─────────────────────┘
```

**Alternative (Simpler) Approach:**
If you want to keep it simple initially, **start with MongoDB only** and migrate financial data to PostgreSQL later when scaling.

---

## Database Schema Design

### **MongoDB Collections (Primary)**

#### **1. franchises**
```javascript
{
  _id: ObjectId("franchise_id"),
  name: "Mumbai Central Franchise",
  slug: "mumbai-central",
  owner: {
    name: "John Doe",
    email: "<EMAIL>",
    phone: "+************",
    documents: {
      pan: "**********",
      gst: "27**********1Z5",
      bankAccount: {
        number: "**********",
        ifsc: "HDFC0001234",
        name: "John Doe"
      }
    }
  },
  location: {
    address: "Mumbai Central, Mumbai, Maharashtra",
    coordinates: [72.8200, 18.9700], // [longitude, latitude]
    serviceAreas: [
      {
        type: "Polygon",
        coordinates: [[[72.8100, 18.9600], [72.8300, 18.9600], [72.8300, 18.9800], [72.8100, 18.9800], [72.8100, 18.9600]]]
      }
    ],
    city: "Mumbai",
    state: "Maharashtra",
    country: "India",
    pincode: "400008"
  },
  businessSettings: {
    operatingHours: {
      monday: { open: "09:00", close: "23:00", isOpen: true },
      tuesday: { open: "09:00", close: "23:00", isOpen: true },
      // ... other days
    },
    deliverySettings: {
      minOrderAmount: 150,
      maxDeliveryDistance: 10,
      avgDeliveryTime: 35,
      deliveryFee: {
        base: 25,
        perKm: 5,
        freeDeliveryAbove: 300
      }
    }
  },
  commissionSettings: {
    restaurantCommission: 0.18, // 18%
    deliveryCommission: 0.15,   // 15%
    platformFee: 0.02,          // 2%
    paymentGatewayFee: 0.025    // 2.5%
  },
  status: "active", // active, inactive, suspended
  onboardingDate: ISODate("2024-01-15"),
  lastActive: ISODate("2024-12-01"),
  createdAt: ISODate("2024-01-01"),
  updatedAt: ISODate("2024-12-01")
}
```

#### **2. users**
```javascript
{
  _id: ObjectId("user_id"),
  email: "<EMAIL>",
  phone: "+************",
  role: "customer", // customer, restaurant_owner, delivery_partner, franchise_admin, super_admin
  franchiseId: ObjectId("franchise_id"), // null for super_admin
  
  // Profile data varies by role
  profile: {
    name: "Customer Name",
    avatar: "https://s3.aws.com/avatar.jpg",
    dateOfBirth: ISODate("1990-05-15"),
    gender: "male",
    
    // Customer-specific
    addresses: [
      {
        _id: ObjectId(),
        type: "home", // home, work, other
        address: "123 Main St, Mumbai",
        coordinates: [72.8200, 18.9700],
        landmark: "Near Metro Station",
        isDefault: true
      }
    ],
    preferences: {
      cuisines: ["indian", "chinese", "italian"],
      dietaryRestrictions: ["vegetarian"],
      spiceLevel: "medium"
    },
    
    // Delivery Partner specific
    deliveryPartnerInfo: {
      vehicleType: "bike", // bike, scooter, bicycle, car
      vehicleNumber: "MH01AB1234",
      drivingLicense: "MH1420110012345",
      documents: {
        license: "url_to_license",
        rc: "url_to_rc",
        insurance: "url_to_insurance",
        photo: "url_to_photo"
      },
      currentLocation: [72.8200, 18.9700],
      isOnline: false,
      rating: 4.5,
      totalDeliveries: 150
    },
    
    // Restaurant Owner specific
    restaurantInfo: {
      restaurantIds: [ObjectId("restaurant_id")]
    }
  },
  
  authentication: {
    passwordHash: "hashed_password",
    emailVerified: true,
    phoneVerified: true,
    lastLogin: ISODate("2024-12-01"),
    resetPasswordToken: null,
    resetPasswordExpiry: null
  },
  
  settings: {
    notifications: {
      push: true,
      sms: true,
      email: false
    },
    language: "en",
    currency: "INR"
  },
  
  status: "active", // active, inactive, suspended, pending_verification
  createdAt: ISODate("2024-01-01"),
  updatedAt: ISODate("2024-12-01")
}
```

#### **3. restaurants**
```javascript
{
  _id: ObjectId("restaurant_id"),
  name: "Burger Palace",
  slug: "burger-palace-mumbai",
  franchiseId: ObjectId("franchise_id"),
  ownerId: ObjectId("user_id"),
  
  businessInfo: {
    type: "restaurant", // restaurant, cloud_kitchen, cafe
    cuisine: ["american", "fast_food"],
    description: "Best burgers in town",
    images: [
      "https://s3.aws.com/restaurant1.jpg",
      "https://s3.aws.com/restaurant2.jpg"
    ],
    license: {
      fssai: "**********1234",
      gst: "27**********1Z5"
    }
  },
  
  location: {
    address: "Shop 123, ABC Mall, Mumbai",
    coordinates: [72.8200, 18.9700],
    landmark: "Near XYZ Metro",
    deliveryZones: [
      {
        type: "Circle",
        center: [72.8200, 18.9700],
        radius: 5000 // 5km radius in meters
      }
    ]
  },
  
  operationalInfo: {
    businessHours: {
      monday: { open: "10:00", close: "22:00", isOpen: true },
      tuesday: { open: "10:00", close: "22:00", isOpen: true },
      // ... other days
    },
    avgPreparationTime: 25, // minutes
    isCurrentlyOpen: true,
    acceptingOrders: true,
    minOrderAmount: 100
  },
  
  menu: [
    {
      _id: ObjectId("menu_item_id"),
      name: "Classic Burger",
      description: "Juicy beef patty with fresh vegetables",
      category: "Burgers",
      price: 250,
      originalPrice: 300,
      images: ["https://s3.aws.com/burger1.jpg"],
      isVegetarian: false,
      isVegan: false,
      spiceLevel: "mild",
      ingredients: ["beef", "lettuce", "tomato", "onion"],
      allergens: ["gluten"],
      nutritionalInfo: {
        calories: 450,
        protein: 25,
        carbs: 35,
        fat: 22
      },
      preparationTime: 15,
      isAvailable: true,
      variants: [
        {
          name: "Regular",
          price: 250,
          isDefault: true
        },
        {
          name: "Large",
          price: 300,
          isDefault: false
        }
      ],
      addons: [
        {
          name: "Extra Cheese",
          price: 30,
          isAvailable: true
        }
      ]
    }
  ],
  
  ratings: {
    average: 4.2,
    count: 150,
    breakdown: {
      5: 80,
      4: 50,
      3: 15,
      2: 3,
      1: 2
    }
  },
  
  financials: {
    commissionRate: 0.18, // 18%
    settlementCycle: "weekly", // daily, weekly, monthly
    bankDetails: {
      accountNumber: "**********",
      ifsc: "HDFC0001234",
      accountName: "Burger Palace"
    }
  },
  
  performance: {
    totalOrders: 1250,
    avgRating: 4.2,
    avgDeliveryTime: 35,
    acceptanceRate: 0.95,
    cancellationRate: 0.05
  },
  
  status: "active", // pending, active, inactive, suspended
  verificationStatus: "verified", // pending, verified, rejected
  onboardingDate: ISODate("2024-02-01"),
  createdAt: ISODate("2024-01-15"),
  updatedAt: ISODate("2024-12-01")
}
```

#### **4. orders**
```javascript
{
  _id: ObjectId("order_id"),
  orderNumber: "ORD-2024-001234",
  franchiseId: ObjectId("franchise_id"),
  customerId: ObjectId("user_id"),
  restaurantId: ObjectId("restaurant_id"),
  deliveryPartnerId: ObjectId("user_id"), // assigned when picked up
  
  items: [
    {
      menuItemId: ObjectId("menu_item_id"),
      name: "Classic Burger",
      price: 250,
      quantity: 2,
      variants: ["Large"],
      addons: [
        {
          name: "Extra Cheese",
          price: 30,
          quantity: 1
        }
      ],
      specialInstructions: "No onions please",
      itemTotal: 530 // (250*2) + (30*1)
    }
  ],
  
  pricing: {
    itemsSubtotal: 530,
    taxes: {
      cgst: 26.5,  // 5%
      sgst: 26.5,  // 5%
      total: 53
    },
    deliveryFee: 25,
    platformFee: 12,
    discounts: {
      couponCode: "FIRST20",
      discountAmount: 50,
      description: "20% off on first order"
    },
    total: 570 // subtotal + taxes + deliveryFee + platformFee - discounts
  },
  
  addresses: {
    pickup: {
      restaurantName: "Burger Palace",
      address: "Shop 123, ABC Mall, Mumbai",
      coordinates: [72.8200, 18.9700],
      contactNumber: "+************"
    },
    delivery: {
      customerName: "John Doe",
      address: "456 Residential Complex, Mumbai",
      coordinates: [72.8300, 18.9800],
      contactNumber: "+919876543211",
      landmark: "Gate 2, Building A",
      deliveryInstructions: "Call when you arrive"
    }
  },
  
  timeline: [
    {
      status: "placed",
      timestamp: ISODate("2024-12-01T10:00:00Z"),
      note: "Order placed by customer"
    },
    {
      status: "accepted",
      timestamp: ISODate("2024-12-01T10:02:00Z"),
      note: "Order accepted by restaurant",
      acceptedBy: ObjectId("user_id")
    },
    {
      status: "preparing",
      timestamp: ISODate("2024-12-01T10:05:00Z"),
      estimatedPreparationTime: 25
    }
    // ... more status updates
  ],
  
  currentStatus: "preparing", // placed, accepted, preparing, ready, picked_up, out_for_delivery, delivered, cancelled
  
  delivery: {
    estimatedPickupTime: ISODate("2024-12-01T10:30:00Z"),
    estimatedDeliveryTime: ISODate("2024-12-01T11:00:00Z"),
    actualPickupTime: null,
    actualDeliveryTime: null,
    deliveryDistance: 3.5, // km
    deliveryRoute: [
      [72.8200, 18.9700],
      [72.8250, 18.9750],
      [72.8300, 18.9800]
    ],
    deliveryPartnerLocation: [72.8250, 18.9750], // real-time location
    deliveryOTP: "1234"
  },
  
  payment: {
    method: "online", // online, cod
    gateway: "razorpay",
    transactionId: "txn_**********",
    status: "completed", // pending, completed, failed, refunded
    paidAmount: 570,
    refundAmount: 0,
    paymentTimestamp: ISODate("2024-12-01T10:00:30Z")
  },
  
  feedback: {
    customerRating: 5,
    customerReview: "Excellent food and quick delivery!",
    restaurantRating: 5,
    deliveryRating: 4,
    reviewTimestamp: ISODate("2024-12-01T11:30:00Z")
  },
  
  createdAt: ISODate("2024-12-01T10:00:00Z"),
  updatedAt: ISODate("2024-12-01T11:00:00Z")
}
```

### **PostgreSQL Tables (Financial)**

#### **1. transactions**
```sql
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id VARCHAR(24) NOT NULL, -- MongoDB ObjectId
    franchise_id VARCHAR(24) NOT NULL,
    transaction_type VARCHAR(20) NOT NULL, -- order_payment, commission_settlement, refund
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    payment_gateway VARCHAR(20),
    gateway_transaction_id VARCHAR(100),
    status VARCHAR(20) NOT NULL, -- pending, completed, failed, cancelled
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **2. commission_settlements**
```sql
CREATE TABLE commission_settlements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    franchise_id VARCHAR(24) NOT NULL,
    restaurant_id VARCHAR(24),
    delivery_partner_id VARCHAR(24),
    settlement_period_start DATE NOT NULL,
    settlement_period_end DATE NOT NULL,
    total_orders INTEGER NOT NULL,
    gross_revenue DECIMAL(12,2) NOT NULL,
    commission_amount DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(10,2) NOT NULL,
    net_settlement DECIMAL(12,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, processed, completed
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

## API Endpoints Specification

### **Base Configuration**
- **Base URL**: `https://api.yourfooddelivery.com/v1`
- **Authentication**: JWT Bearer Token
- **Content-Type**: `application/json`
- **Rate Limiting**: 1000 requests/hour per user

### **1. Authentication APIs**

#### **POST /auth/register**
Register a new user
```javascript
// Request
{
  "email": "<EMAIL>",
  "phone": "+************",
  "password": "securePassword123",
  "role": "customer",
  "franchiseId": "franchise_id", // optional for customers
  "profile": {
    "name": "John Doe"
  }
}

// Response
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "userId": "user_id",
    "email": "<EMAIL>",
    "role": "customer",
    "otpSent": true
  }
}
```

#### **POST /auth/verify-otp**
Verify phone number with OTP
```javascript
// Request
{
  "phone": "+************",
  "otp": "123456"
}

// Response
{
  "success": true,
  "message": "Phone verified successfully",
  "data": {
    "token": "jwt_token_here",
    "refreshToken": "refresh_token_here",
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "role": "customer",
      "profile": { ... }
    }
  }
}
```

#### **POST /auth/login**
User login
```javascript
// Request
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}

// Response - Same as verify-otp
```

### **2. Franchise Management APIs**

#### **POST /franchises**
Create a new franchise (Super Admin only)
```javascript
// Request
{
  "name": "Mumbai Central Franchise",
  "owner": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+************"
  },
  "location": {
    "address": "Mumbai Central, Mumbai",
    "coordinates": [72.8200, 18.9700],
    "city": "Mumbai",
    "state": "Maharashtra"
  },
  "commissionSettings": {
    "restaurantCommission": 0.18,
    "deliveryCommission": 0.15
  }
}

// Response
{
  "success": true,
  "message": "Franchise created successfully",
  "data": {
    "franchiseId": "franchise_id",
    "name": "Mumbai Central Franchise",
    "slug": "mumbai-central",
    "status": "active"
  }
}
```

#### **GET /franchises**
Get all franchises (Super Admin) or specific franchise (Franchise Admin)
```javascript
// Response
{
  "success": true,
  "data": [
    {
      "id": "franchise_id",
      "name": "Mumbai Central Franchise",
      "location": { ... },
      "status": "active",
      "totalRestaurants": 25,
      "totalOrders": 1250,
      "monthlyRevenue": 125000
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 5
  }
}
```

#### **PUT /franchises/:id**
Update franchise settings
```javascript
// Request
{
  "commissionSettings": {
    "restaurantCommission": 0.20,
    "deliveryCommission": 0.15
  },
  "businessSettings": {
    "minOrderAmount": 200
  }
}

// Response
{
  "success": true,
  "message": "Franchise updated successfully"
}
```

### **3. Restaurant Management APIs**

#### **POST /restaurants**
Register a new restaurant
```javascript
// Request
{
  "name": "Burger Palace",
  "franchiseId": "franchise_id",
  "businessInfo": {
    "type": "restaurant",
    "cuisine": ["american", "fast_food"],
    "description": "Best burgers in town"
  },
  "location": {
    "address": "Shop 123, ABC Mall, Mumbai",
    "coordinates": [72.8200, 18.9700]
  },
  "businessHours": {
    "monday": { "open": "10:00", "close": "22:00", "isOpen": true }
  }
}

// Response
{
  "success": true,
  "message": "Restaurant registered successfully",
  "data": {
    "restaurantId": "restaurant_id",
    "status": "pending", // pending approval
    "verificationRequired": [
      "fssai_license",
      "gst_certificate",
      "bank_details"
    ]
  }
}
```

#### **GET /restaurants**
Get restaurants (with filters)
```javascript
// Query Parameters: ?franchiseId=xxx&cuisine=indian&isOpen=true&page=1&limit=10

// Response
{
  "success": true,
  "data": [
    {
      "id": "restaurant_id",
      "name": "Burger Palace",
      "cuisine": ["american"],
      "rating": 4.2,
      "avgDeliveryTime": 35,
      "minOrderAmount": 100,
      "isCurrentlyOpen": true,
      "distance": 2.5,
      "deliveryFee": 25,
      "images": ["url1", "url2"]
    }
  ],
  "pagination": { ... }
}
```

#### **GET /restaurants/:id/menu**
Get restaurant menu
```javascript
// Response
{
  "success": true,
  "data": {
    "restaurantInfo": {
      "id": "restaurant_id",
      "name": "Burger Palace",
      "isCurrentlyOpen": true
    },
    "categories": [
      {
        "name": "Burgers",
        "items": [
          {
            "id": "menu_item_id",
            "name": "Classic Burger",
            "price": 250,
            "originalPrice": 300,
            "isAvailable": true,
            "preparationTime": 15,
            "rating": 4.5,
            "variants": [...],
            "addons": [...]
          }
        ]
      }
    ]
  }
}
```

### **4. Order Management APIs**

#### **POST /orders**
Place a new order
```javascript
// Request
{
  "restaurantId": "restaurant_id",
  "items": [
    {
      "menuItemId": "menu_item_id",
      "quantity": 2,
      "variants": ["Large"],
      "addons": [
        {
          "name": "Extra Cheese",
          "price": 30,
          "quantity": 1
        }
      ],
      "specialInstructions": "No onions"
    }
  ],
  "deliveryAddress": {
    "address": "456 Complex, Mumbai",
    "coordinates": [72.8300, 18.9800],
    "contactNumber": "+919876543211"
  },
  "paymentMethod": "online",
  "couponCode": "FIRST20"
}

// Response
{
  "success": true,
  "message": "Order placed successfully",
  "data": {
    "orderId": "order_id",
    "orderNumber": "ORD-2024-001234",
    "estimatedDeliveryTime": "2024-12-01T11:00:00Z",
    "totalAmount": 570,
    "paymentDetails": {
      "gateway": "razorpay",
      "paymentUrl": "https://razorpay.com/payment/...",
      "orderId": "rzp_order_id"
    }
  }
}
```

#### **GET /orders/:id**
Get order details
```javascript
// Response
{
  "success": true,
  "data": {
    "id": "order_id",
    "orderNumber": "ORD-2024-001234",
    "status": "preparing",
    "restaurant": {
      "name": "Burger Palace",
      "phone": "+************"
    },
    "items": [...],
    "pricing": {...},
    "timeline": [...],
    "delivery": {
      "estimatedDeliveryTime": "2024-12-01T11:00:00Z",
      "deliveryPartner": {
        "name": "Delivery Guy",
        "phone": "+919876543212",
        "currentLocation": [72.8250, 18.9750]
      }
    }
  }
}
```

#### **PUT /orders/:id/status**
Update order status (Restaurant/Delivery Partner)
```javascript
// Request
{
  "status": "accepted",
  "estimatedPreparationTime": 25,
  "note": "Order confirmed"
}

// Response
{
  "success": true,
  "message": "Order status updated successfully"
}
```

### **5. User Management APIs**

#### **GET /users/profile**
Get user profile
```javascript
// Response
{
  "success": true,
  "data": {
    "id": "user_id",
    "email": "<EMAIL>",
    "phone": "+************",
    "role": "customer",
    "profile": {
      "name": "John Doe",
      "addresses": [...],
      "preferences": {...}
    },
    "stats": {
      "totalOrders": 25,
      "totalSpent": 5000,
      "favoriteRestaurants": [...]
    }
  }
}
```

#### **PUT /users/profile**
Update user profile
```javascript
// Request
{
  "profile": {
    "name": "John Smith",
    "preferences": {
      "cuisines": ["indian", "chinese"]
    }
  }
}

// Response
{
  "success": true,
  "message": "Profile updated successfully"
}
```

---

## API Development Guidelines

### **1. Error Handling**
```javascript
// Standard Error Response Format
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  },
  "timestamp": "2024-12-01T10:00:00Z",
  "requestId": "req_123456"
}
```

### **2. Validation Rules**
- **Email**: Valid email format, unique across system
- **Phone**: Valid format with country code, unique per role
- **Password**: Minimum 8 characters, alphanumeric
- **Coordinates**: Valid longitude/latitude pairs
- **Currency**: ISO 4217 format (INR, USD)
- **Status**: Predefined enum values only

### **3. Authentication Middleware**
```javascript
// JWT Token Structure
{
  "userId": "user_id",
  "email": "<EMAIL>",
  "role": "customer",
  "franchiseId": "franchise_id",
  "iat": 1701432000,
  "exp": 1701518400
}

// Role-based Access Control
const rolePermissions = {
  customer: ['orders:create', 'orders:read', 'profile:update'],
  restaurant_owner: ['restaurant:update', 'orders:update', 'menu:manage'],
  delivery_partner: ['orders:update', 'location:update'],
  franchise_admin: ['franchise:read', 'restaurants:manage', 'reports:read'],
  super_admin: ['*'] // all permissions
};
```

### **4. Database Indexes**
```javascript
// MongoDB Indexes for Performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "phone": 1, "role": 1 }, { unique: true });
db.restaurants.createIndex({ "location.coordinates": "2dsphere" });
db.restaurants.createIndex({ "franchiseId": 1, "status": 1 });
db.orders.createIndex({ "customerId": 1, "createdAt": -1 });
db.orders.createIndex({ "restaurantId": 1, "currentStatus": 1 });
db.orders.createIndex({ "deliveryPartnerId": 1, "currentStatus": 1 });
```

### **5. Real-time Events**
```javascript
// Socket.io Event Structure
const socketEvents = {
  // Order events
  ORDER_PLACED: 'order:placed',
  ORDER_STATUS_UPDATED: 'order:status_updated',
  DELIVERY_LOCATION_UPDATED: 'delivery:location_updated',
  
  // Restaurant events
  NEW_ORDER_RECEIVED: 'restaurant:new_order',
  
  // Delivery partner events
  ORDER_ASSIGNED: 'delivery:order_assigned',
  
  // Customer events
  ORDER_TRACKING: 'customer:order_tracking'
};
```

---

## Development Setup Instructions

### **1. Environment Setup**
```bash
# Clone repository
git clone <repository_url>
cd food-delivery-api

# Install dependencies
npm install

# Environment variables (.env file)
NODE_ENV=development
PORT=3000
MONGODB_URI=mongodb://localhost:27017/food_delivery
POSTGRESQL_URI=postgresql://user:password@localhost:5432/food_delivery_financial
JWT_SECRET=your_jwt_secret_key_here
RAZORPAY_KEY_ID=your_razorpay_key
RAZORPAY_SECRET=your_razorpay_secret
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
GOOGLE_MAPS_API_KEY=your_google_maps_key
REDIS_URL=redis://localhost:6379
```

### **2. Database Setup**

#### **MongoDB Setup**
```bash
# Install MongoDB locally or use MongoDB Atlas
# Create database and collections
mongosh

use food_delivery

# Create collections with validation
db.createCollection("franchises", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["name", "owner", "location", "commissionSettings"],
      properties: {
        name: { bsonType: "string", minLength: 3, maxLength: 100 },
        "owner.email": { bsonType: "string", pattern: "^.+@.+\..+$" },
        "owner.phone": { bsonType: "string", pattern: "^\\+[1-9]\\d{1,14}$" },
        "location.coordinates": { 
          bsonType: "array",
          minItems: 2,
          maxItems: 2,
          items: { bsonType: "double" }
        },
        status: { enum: ["active", "inactive", "suspended"] }
      }
    }
  }
});

# Create indexes
db.franchises.createIndex({ "location.coordinates": "2dsphere" });
db.franchises.createIndex({ "slug": 1 }, { unique: true });
db.franchises.createIndex({ "owner.email": 1 }, { unique: true });
```

#### **PostgreSQL Setup**
```sql
-- Create database
CREATE DATABASE food_delivery_financial;

-- Connect to database
\c food_delivery_financial;

-- Create tables
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id VARCHAR(24) NOT NULL,
    franchise_id VARCHAR(24) NOT NULL,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('order_payment', 'commission_settlement', 'refund', 'adjustment')),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    payment_gateway VARCHAR(20),
    gateway_transaction_id VARCHAR(100),
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE commission_settlements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    franchise_id VARCHAR(24) NOT NULL,
    restaurant_id VARCHAR(24),
    delivery_partner_id VARCHAR(24),
    settlement_period_start DATE NOT NULL,
    settlement_period_end DATE NOT NULL,
    total_orders INTEGER NOT NULL DEFAULT 0,
    gross_revenue DECIMAL(12,2) NOT NULL DEFAULT 0,
    commission_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    net_settlement DECIMAL(12,2) NOT NULL DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'completed', 'failed')),
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_transactions_order_id ON transactions(order_id);
CREATE INDEX idx_transactions_franchise_id ON transactions(franchise_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);

CREATE INDEX idx_settlements_franchise_id ON commission_settlements(franchise_id);
CREATE INDEX idx_settlements_period ON commission_settlements(settlement_period_start, settlement_period_end);
CREATE INDEX idx_settlements_status ON commission_settlements(status);
```

### **3. Project Structure**
```
food-delivery-api/
├── src/
│   ├── controllers/
│   │   ├── authController.js
│   │   ├── franchiseController.js
│   │   ├── restaurantController.js
│   │   ├── orderController.js
│   │   └── userController.js
│   ├── models/
│   │   ├── mongodb/
│   │   │   ├── Franchise.js
│   │   │   ├── User.js
│   │   │   ├── Restaurant.js
│   │   │   └── Order.js
│   │   └── postgresql/
│   │       ├── Transaction.js
│   │       └── Settlement.js
│   ├── routes/
│   │   ├── auth.js
│   │   ├── franchises.js
│   │   ├── restaurants.js
│   │   ├── orders.js
│   │   └── users.js
│   ├── middleware/
│   │   ├── auth.js
│   │   ├── validation.js
│   │   ├── rateLimiting.js
│   │   └── errorHandler.js
│   ├── services/
│   │   ├── authService.js
│   │   ├── paymentService.js
│   │   ├── locationService.js
│   │   ├── notificationService.js
│   │   └── socketService.js
│   ├── utils/
│   │   ├── database.js
│   │   ├── logger.js
│   │   ├── helpers.js
│   │   └── constants.js
│   ├── config/
│   │   ├── database.js
│   │   ├── redis.js
│   │   └── socket.js
│   └── app.js
├── tests/
│   ├── unit/
│   ├── integration/
│   └── fixtures/
├── docs/
│   ├── api-documentation.md
│   └── database-schema.md
├── package.json
├── .env.example
├── .gitignore
└── README.md
```

### **4. Code Implementation Examples**

#### **MongoDB Connection (src/config/database.js)**
```javascript
const mongoose = require('mongoose');
const { Pool } = require('pg');

class DatabaseManager {
  constructor() {
    this.mongoConnection = null;
    this.pgPool = null;
  }

  async connectMongoDB() {
    try {
      this.mongoConnection = await mongoose.connect(process.env.MONGODB_URI, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      });
      
      console.log('✅ MongoDB connected successfully');
      
      // Set up connection event listeners
      mongoose.connection.on('error', (err) => {
        console.error('❌ MongoDB connection error:', err);
      });
      
      mongoose.connection.on('disconnected', () => {
        console.warn('⚠️ MongoDB disconnected');
      });
      
      return this.mongoConnection;
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error);
      process.exit(1);
    }
  }

  async connectPostgreSQL() {
    try {
      this.pgPool = new Pool({
        connectionString: process.env.POSTGRESQL_URI,
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });
      
      // Test the connection
      const client = await this.pgPool.connect();
      await client.query('SELECT NOW()');
      client.release();
      
      console.log('✅ PostgreSQL connected successfully');
      return this.pgPool;
    } catch (error) {
      console.error('❌ PostgreSQL connection failed:', error);
      process.exit(1);
    }
  }

  async disconnect() {
    if (this.mongoConnection) {
      await mongoose.disconnect();
      console.log('🔌 MongoDB disconnected');
    }
    
    if (this.pgPool) {
      await this.pgPool.end();
      console.log('🔌 PostgreSQL disconnected');
    }
  }
}

module.exports = new DatabaseManager();
```

#### **Franchise Model (src/models/mongodb/Franchise.js)**
```javascript
const mongoose = require('mongoose');

const franchiseSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    minlength: 3,
    maxlength: 100
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  owner: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Invalid email format']
    },
    phone: {
      type: String,
      required: true,
      match: [/^\+[1-9]\d{1,14}$/, 'Invalid phone number format']
    },
    documents: {
      pan: String,
      gst: String,
      bankAccount: {
        number: String,
        ifsc: String,
        name: String
      }
    }
  },
  location: {
    address: {
      type: String,
      required: true
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: true,
      validate: {
        validator: function(coords) {
          return coords.length === 2 && 
                 coords[0] >= -180 && coords[0] <= 180 && // longitude
                 coords[1] >= -90 && coords[1] <= 90;     // latitude
        },
        message: 'Invalid coordinates format'
      }
    },
    serviceAreas: [{
      type: {
        type: String,
        enum: ['Polygon', 'Circle'],
        default: 'Circle'
      },
      coordinates: [[Number]], // For Polygon
      center: [Number],         // For Circle [lng, lat]
      radius: Number            // For Circle (in meters)
    }],
    city: {
      type: String,
      required: true
    },
    state: {
      type: String,
      required: true
    },
    country: {
      type: String,
      required: true,
      default: 'India'
    },
    pincode: String
  },
  businessSettings: {
    operatingHours: {
      monday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      tuesday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      wednesday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      thursday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      friday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      saturday: { open: String, close: String, isOpen: { type: Boolean, default: true } },
      sunday: { open: String, close: String, isOpen: { type: Boolean, default: true } }
    },
    deliverySettings: {
      minOrderAmount: {
        type: Number,
        default: 100,
        min: 0
      },
      maxDeliveryDistance: {
        type: Number,
        default: 10,
        min: 1
      },
      avgDeliveryTime: {
        type: Number,
        default: 30,
        min: 10
      },
      deliveryFee: {
        base: {
          type: Number,
          default: 25,
          min: 0
        },
        perKm: {
          type: Number,
          default: 5,
          min: 0
        },
        freeDeliveryAbove: {
          type: Number,
          default: 300,
          min: 0
        }
      }
    }
  },
  commissionSettings: {
    restaurantCommission: {
      type: Number,
      required: true,
      min: 0,
      max: 1,
      default: 0.18
    },
    deliveryCommission: {
      type: Number,
      required: true,
      min: 0,
      max: 1,
      default: 0.15
    },
    platformFee: {
      type: Number,
      required: true,
      min: 0,
      max: 1,
      default: 0.02
    },
    paymentGatewayFee: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.025
    }
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  },
  onboardingDate: {
    type: Date,
    default: Date.now
  },
  lastActive: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create 2dsphere index for location-based queries
franchiseSchema.index({ 'location.coordinates': '2dsphere' });
franchiseSchema.index({ slug: 1 }, { unique: true });
franchiseSchema.index({ 'owner.email': 1 }, { unique: true });
franchiseSchema.index({ status: 1 });

// Virtual for total restaurants
franchiseSchema.virtual('totalRestaurants', {
  ref: 'Restaurant',
  localField: '_id',
  foreignField: 'franchiseId',
  count: true
});

// Pre-save middleware to generate slug
franchiseSchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.slug = this.name.toLowerCase()
                      .replace(/[^a-z0-9\s-]/g, '')
                      .replace(/\s+/g, '-')
                      .replace(/-+/g, '-')
                      .trim('-');
  }
  next();
});

// Instance method to check if franchise serves a location
franchiseSchema.methods.servesLocation = function(coordinates) {
  // Implementation for checking if coordinates fall within service areas
  const [longitude, latitude] = coordinates;
  
  for (const area of this.location.serviceAreas) {
    if (area.type === 'Circle') {
      const distance = this.calculateDistance(area.center, coordinates);
      if (distance <= area.radius) return true;
    }
    // Add polygon check logic here if needed
  }
  
  return false;
};

// Helper method to calculate distance between two points
franchiseSchema.methods.calculateDistance = function(point1, point2) {
  const [lon1, lat1] = point1;
  const [lon2, lat2] = point2;
  
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI/180;
  const φ2 = lat2 * Math.PI/180;
  const Δφ = (lat2-lat1) * Math.PI/180;
  const Δλ = (lon2-lon1) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c;
};

module.exports = mongoose.model('Franchise', franchiseSchema);
```

#### **Auth Controller (src/controllers/authController.js)**
```javascript
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/mongodb/User');
const authService = require('../services/authService');
const { validationResult } = require('express-validator');

class AuthController {
  async register(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid input data',
            details: errors.array()
          }
        });
      }

      const { email, phone, password, role, franchiseId, profile } = req.body;

      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [
          { email },
          { phone, role }
        ]
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          error: {
            code: 'USER_EXISTS',
            message: 'User with this email or phone already exists'
          }
        });
      }

      // Hash password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // Create user
      const user = new User({
        email,
        phone,
        role,
        franchiseId: role === 'super_admin' ? null : franchiseId,
        profile,
        authentication: {
          passwordHash,
          emailVerified: false,
          phoneVerified: false
        }
      });

      await user.save();

      // Send OTP for phone verification
      const otpResult = await authService.sendOTP(phone);

      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        data: {
          userId: user._id,
          email: user.email,
          role: user.role,
          otpSent: otpResult.success
        }
      });

    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Registration failed'
        }
      });
    }
  }

  async verifyOTP(req, res) {
    try {
      const { phone, otp } = req.body;

      // Verify OTP
      const otpValid = await authService.verifyOTP(phone, otp);
      if (!otpValid) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_OTP',
            message: 'Invalid or expired OTP'
          }
        });
      }

      // Find user and mark phone as verified
      const user = await User.findOne({ phone });
      if (!user) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found'
          }
        });
      }

      user.authentication.phoneVerified = true;
      await user.save();

      // Generate JWT tokens
      const tokens = authService.generateTokens(user);

      // Update last login
      user.authentication.lastLogin = new Date();
      await user.save();

      res.json({
        success: true,
        message: 'Phone verified successfully',
        data: {
          token: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          user: {
            id: user._id,
            email: user.email,
            phone: user.phone,
            role: user.role,
            profile: user.profile,
            franchiseId: user.franchiseId
          }
        }
      });

    } catch (error) {
      console.error('OTP verification error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'OTP verification failed'
        }
      });
    }
  }

  async login(req, res) {
    try {
      const { email, password } = req.body;

      // Find user
      const user = await User.findOne({ email }).select('+authentication.passwordHash');
      if (!user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password'
          }
        });
      }

      // Check password
      const passwordValid = await bcrypt.compare(password, user.authentication.passwordHash);
      if (!passwordValid) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password'
          }
        });
      }

      // Check if user is active
      if (user.status !== 'active') {
        return res.status(403).json({
          success: false,
          error: {
            code: 'ACCOUNT_INACTIVE',
            message: 'Account is inactive or suspended'
          }
        });
      }

      // Generate tokens
      const tokens = authService.generateTokens(user);

      // Update last login
      user.authentication.lastLogin = new Date();
      await user.save();

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          token: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          user: {
            id: user._id,
            email: user.email,
            phone: user.phone,
            role: user.role,
            profile: user.profile,
            franchiseId: user.franchiseId
          }
        }
      });

    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Login failed'
        }
      });
    }
  }

  async refreshToken(req, res) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'MISSING_TOKEN',
            message: 'Refresh token is required'
          }
        });
      }

      // Verify refresh token
      const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
      const user = await User.findById(decoded.userId);

      if (!user || user.status !== 'active') {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: 'Invalid refresh token'
          }
        });
      }

      // Generate new tokens
      const tokens = authService.generateTokens(user);

      res.json({
        success: true,
        data: {
          token: tokens.accessToken,
          refreshToken: tokens.refreshToken
        }
      });

    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid refresh token'
        }
      });
    }
  }

  async logout(req, res) {
    try {
      // In a production app, you might want to blacklist the token
      // For now, we'll just return success
      res.json({
        success: true,
        message: 'Logged out successfully'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Logout failed'
        }
      });
    }
  }
}

module.exports = new AuthController();
```

#### **Auth Middleware (src/middleware/auth.js)**
```javascript
const jwt = require('jsonwebtoken');
const User = require('../models/mongodb/User');

class AuthMiddleware {
  // Verify JWT token
  async authenticateToken(req, res, next) {
    try {
      const authHeader = req.headers['authorization'];
      const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

      if (!token) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'MISSING_TOKEN',
            message: 'Access token is required'
          }
        });
      }

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.userId);

      if (!user || user.status !== 'active') {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: 'Invalid or expired token'
          }
        });
      }

      // Add user to request object
      req.user = user;
      next();

    } catch (error) {
      if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: 'Invalid token'
          }
        });
      } else if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          error: {
            code: 'TOKEN_EXPIRED',
            message: 'Token has expired'
          }
        });
      } else {
        console.error('Authentication error:', error);
        return res.status(500).json({
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Authentication failed'
          }
        });
      }
    }
  }

  // Check user role
  requireRole(roles) {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          }
        });
      }

      const userRole = req.user.role;
      const allowedRoles = Array.isArray(roles) ? roles : [roles];

      if (!allowedRoles.includes(userRole)) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Insufficient permissions for this action'
          }
        });
      }

      next();
    };
  }

  // Check franchise access
  requireFranchiseAccess(req, res, next) {
    const userFranchiseId = req.user.franchiseId?.toString();
    const requestedFranchiseId = req.params.franchiseId || req.body.franchiseId;

    // Super admin can access all franchises
    if (req.user.role === 'super_admin') {
      return next();
    }

    // Check if user belongs to the requested franchise
    if (!userFranchiseId || userFranchiseId !== requestedFranchiseId) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FRANCHISE_ACCESS_DENIED',
          message: 'Access denied for this franchise'
        }
      });
    }

    next();
  }

  // Optional authentication (for public endpoints that benefit from user context)
  optionalAuth(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      User.findById(decoded.userId)
        .then(user => {
          req.user = user && user.status === 'active' ? user : null;
          next();
        })
        .catch(() => {
          req.user = null;
          next();
        });
    } catch (error) {
      req.user = null;
      next();
    }
  }
}

module.exports = new AuthMiddleware();
```

### **5. Testing Setup**

#### **Test Configuration (package.json)**
```json
{
  "scripts": {
    "dev": "nodemon src/app.js",
    "start": "node src/app.js",
    "test": "jest --detectOpenHandles",
    "test:watch": "jest --watch --detectOpenHandles",
    "test:coverage": "jest --coverage --detectOpenHandles"
  },
  "jest": {
    "testEnvironment": "node",
    "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"],
    "testMatch": ["**/tests/**/*.test.js"],
    "collectCoverageFrom": [
      "src/**/*.js",
      "!src/app.js",
      "!src/config/**"
    ]
  }
}
```

#### **Sample Test (tests/unit/authController.test.js)**
```javascript
const request = require('supertest');
const app = require('../../src/app');
const User = require('../../src/models/mongodb/User');
const authService = require('../../src/services/authService');

describe('Auth Controller', () => {
  beforeEach(async () => {
    await User.deleteMany({});
  });

  describe('POST /auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        phone: '+************',
        password: 'password123',
        role: 'customer',
        profile: {
          name: 'Test User'
        }
      };

      // Mock OTP service
      jest.spyOn(authService, 'sendOTP').mockResolvedValue({ success: true });

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.email).toBe(userData.email);
      expect(response.body.data.otpSent).toBe(true);

      // Verify user was created in database
      const user = await User.findOne({ email: userData.email });
      expect(user).toBeTruthy();
      expect(user.role).toBe('customer');
    });

    it('should return error for duplicate email', async () => {
      // Create existing user
      await User.create({
        email: '<EMAIL>',
        phone: '+************',
        role: 'customer',
        authentication: {
          passwordHash: 'hashedpassword'
        }
      });

      const userData = {
        email: '<EMAIL>',
        phone: '+919876543211',
        password: 'password123',
        role: 'customer'
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('USER_EXISTS');
    });
  });
});
```

### **6. API Documentation Setup**

#### **Swagger Configuration (src/config/swagger.js)**
```javascript
const swaggerJsdoc = require('swagger-jsdoc');
const swagger