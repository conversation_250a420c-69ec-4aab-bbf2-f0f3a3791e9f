#!/usr/bin/env node

/**
 * Database Setup Script
 * Automatically sets up MongoDB and PostgreSQL databases
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const logger = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  error: (msg) => console.log(`❌ ${msg}`),
  warn: (msg) => console.log(`⚠️  ${msg}`)
};

async function setupMongoDB() {
  try {
    logger.info('Setting up MongoDB...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    
    logger.success('Connected to MongoDB');
    
    // Read and execute MongoDB setup script
    const mongoScript = fs.readFileSync(path.join(__dirname, 'setup-mongodb.js'), 'utf8');
    
    // Extract the JavaScript commands (skip the mongosh specific commands)
    const db = mongoose.connection.db;
    
    // Create collections with validation
    const collections = ['users', 'franchises', 'restaurants', 'orders', 'reviews', 'coupons'];
    
    for (const collectionName of collections) {
      try {
        await db.createCollection(collectionName);
        logger.success(`Created collection: ${collectionName}`);
      } catch (error) {
        if (error.code === 48) { // Collection already exists
          logger.warn(`Collection ${collectionName} already exists`);
        } else {
          throw error;
        }
      }
    }
    
    // Create indexes
    const User = require('../src/models/User');
    const Franchise = require('../src/models/Franchise');
    const Restaurant = require('../src/models/Restaurant');
    const Order = require('../src/models/Order');
    
    // The indexes will be created automatically when the models are loaded
    logger.success('MongoDB indexes will be created automatically by Mongoose models');
    
    await mongoose.disconnect();
    logger.success('MongoDB setup completed');
    
  } catch (error) {
    logger.error(`MongoDB setup failed: ${error.message}`);
    throw error;
  }
}

async function setupPostgreSQL() {
  try {
    logger.info('Setting up PostgreSQL...');
    
    // Connect to PostgreSQL
    const pool = new Pool({
      host: process.env.POSTGRESQL_HOST || 'localhost',
      port: process.env.POSTGRESQL_PORT || 5432,
      user: process.env.POSTGRESQL_USER || 'myuser',
      password: process.env.POSTGRESQL_PASSWORD || 'mypassword',
      database: process.env.POSTGRESQL_DB || 'mydatabase',
    });
    
    // Test connection
    const client = await pool.connect();
    logger.success('Connected to PostgreSQL');
    
    // Read and execute PostgreSQL setup script
    const sqlScript = fs.readFileSync(path.join(__dirname, 'setup-postgresql.sql'), 'utf8');
    
    // Execute the SQL script
    await client.query(sqlScript);
    logger.success('PostgreSQL tables and indexes created');
    
    client.release();
    await pool.end();
    logger.success('PostgreSQL setup completed');
    
  } catch (error) {
    logger.error(`PostgreSQL setup failed: ${error.message}`);
    throw error;
  }
}

async function main() {
  try {
    logger.info('🚀 Starting database setup...');
    
    // Check environment variables
    if (!process.env.MONGODB_URI) {
      throw new Error('MONGODB_URI environment variable is required');
    }
    
    if (!process.env.POSTGRESQL_USER || !process.env.POSTGRESQL_PASSWORD) {
      throw new Error('PostgreSQL credentials are required');
    }
    
    // Setup databases
    await setupMongoDB();
    await setupPostgreSQL();
    
    logger.success('🎉 All databases setup completed successfully!');
    logger.info('📱 Your Food Delivery API is ready to use!');
    logger.info('🔗 Start the server with: npm run dev');
    
  } catch (error) {
    logger.error(`Database setup failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  main();
}

module.exports = { setupMongoDB, setupPostgreSQL };
