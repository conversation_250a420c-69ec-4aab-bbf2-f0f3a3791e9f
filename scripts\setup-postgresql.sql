-- Food Delivery Platform - PostgreSQL Setup Script
-- Run this script to create the financial database tables

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id SERIAL PRIMARY KEY,
    order_id VARCHAR(24) NOT NULL,
    franchise_id VARCHAR(24) NOT NULL,
    transaction_type VARCHAR(30) NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    payment_gateway VARCHAR(20),
    gateway_transaction_id VARCHAR(100),
    gateway_response TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    metadata TEXT,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create commission_settlements table
CREATE TABLE IF NOT EXISTS commission_settlements (
    id SERIAL PRIMARY KEY,
    franchise_id VARCHAR(24) NOT NULL,
    restaurant_id VARCHAR(24),
    delivery_partner_id VARCHAR(24),
    settlement_type VARCHAR(20) NOT NULL,
    settlement_period_start DATE NOT NULL,
    settlement_period_end DATE NOT NULL,
    total_orders INTEGER NOT NULL DEFAULT 0,
    gross_revenue DECIMAL(15,2) NOT NULL DEFAULT 0,
    commission_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    adjustment_amount DECIMAL(10,2) DEFAULT 0,
    net_settlement DECIMAL(12,2) NOT NULL DEFAULT 0,
    payment_method VARCHAR(20) DEFAULT 'bank_transfer',
    bank_reference_number VARCHAR(50),
    utr_number VARCHAR(50),
    status VARCHAR(20) DEFAULT 'pending',
    settlement_details TEXT,
    notes TEXT,
    processed_by VARCHAR(24),
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(20) NOT NULL,
    entity_id VARCHAR(24) NOT NULL,
    action VARCHAR(30) NOT NULL,
    performed_by VARCHAR(24) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    old_values TEXT,
    new_values TEXT,
    changes TEXT,
    reason TEXT,
    metadata TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for transactions
CREATE INDEX idx_transactions_order_id ON transactions(order_id);
CREATE INDEX idx_transactions_franchise_id ON transactions(franchise_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);

-- Create indexes for commission_settlements
CREATE INDEX idx_settlements_franchise_id ON commission_settlements(franchise_id);
CREATE INDEX idx_settlements_restaurant_id ON commission_settlements(restaurant_id);
CREATE INDEX idx_settlements_delivery_partner_id ON commission_settlements(delivery_partner_id);
CREATE INDEX idx_settlements_status ON commission_settlements(status);
CREATE INDEX idx_settlements_type ON commission_settlements(settlement_type);

-- Create indexes for audit_logs
CREATE INDEX idx_audit_logs_entity ON audit_logs(entity_type, entity_id);
CREATE INDEX idx_audit_logs_performed_by ON audit_logs(performed_by);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- Insert sample data for testing
INSERT INTO transactions (order_id, franchise_id, transaction_type, amount, status)
VALUES ('507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012', 'order_payment', 570.00, 'completed')
ON CONFLICT DO NOTHING;
